#!/usr/bin/env python3
"""
Setup script for Chaplin - Video-to-Text Application
This script helps users properly configure the application with correct model paths.
"""

import os
import sys
import shutil
from pathlib import Path

def create_model_directories():
    """Create necessary model directories if they don't exist."""
    model_dirs = [
        "benchmarks/LRS3/models/LRS3_V_WER19.1",
        "benchmarks/LRS3/language_models/lm_en_subword"
    ]
    
    for dir_path in model_dirs:
        full_path = Path(dir_path)
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {full_path}")

def check_model_files():
    """Check if required model files exist."""
    required_files = [
        "benchmarks/LRS3/models/LRS3_V_WER19.1/model.pth",
        "benchmarks/LRS3/models/LRS3_V_WER19.1/model.json",
        "benchmarks/LRS3/language_models/lm_en_subword/model.pth",
        "benchmarks/LRS3/language_models/lm_en_subword/model.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️  Missing model files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease download the required model files from the official repository.")
        return False
    
    print("✅ All required model files are present.")
    return True

def update_config_files():
    """Update configuration files with correct paths."""
    config_files = [
        "hydra_configs/default.yaml",
        "hydra_configs/model_config.yaml"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file} exists")
        else:
            print(f"❌ {config_file} missing")

def main():
    """Main setup function."""
    print("🔧 Setting up Chaplin - Video-to-Text Application")
    print("=" * 50)
    
    # Create necessary directories
    create_model_directories()
    
    # Check model files
    models_ready = check_model_files()
    
    # Check configuration files
    update_config_files()
    
    print("\n📋 Setup Summary:")
    print("=" * 50)
    
    if models_ready:
        print("✅ Setup complete! You can now run:")
        print("   python main.py")
    else:
        print("⚠️  Setup incomplete. Please:")
        print("   1. Download the required model files")
        print("   2. Place them in the correct directories")
        print("   3. Run this script again")
    
    print("\n📝 Next Steps:")
    print("   1. Press 'Alt' to start/stop recording")
    print("   2. Press 'q' to quit the application")
    print("   3. Check the README.md for detailed usage instructions")

if __name__ == "__main__":
    main()

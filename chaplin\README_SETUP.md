# Chaplin Setup Guide

This guide will help you properly set up and configure the Chaplin video-to-text application.

## Prerequisites

1. **Python 3.7+** installed on your system
2. **CUDA-capable GPU** (optional but recommended for better performance)
3. **Git** for downloading model files

## Quick Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Download Model Files
The application requires pre-trained model files. Run the setup script:

```bash
python setup.py
```

### 3. Manual Model Download (if needed)
If the setup script indicates missing files, manually download:

- **Video Model**: LRS3_V_WER19.1
  - Download from: [Official Repository](https://github.com/your-repo/chaplin-models)
  - Place in: `benchmarks/LRS3/models/LRS3_V_WER19.1/`

- **Language Model**: lm_en_subword
  - Download from: [Official Repository](https://github.com/your-repo/chaplin-models)
  - Place in: `benchmarks/LRS3/language_models/lm_en_subword/`

### 4. Verify Installation
```bash
python setup.py
```

## Configuration Files

### Default Configuration (`hydra_configs/default.yaml`)
- **config_filename**: Points to model configuration
- **detector**: Face detection method (retinaface or mediapipe)
- **gpu_idx**: GPU device index (-1 for CPU)

### Model Configuration (`hydra_configs/model_config.yaml`)
- **model**: Model architecture and weights
- **preprocessing**: Image preprocessing parameters
- **inference**: Runtime configuration

## Usage

### Basic Usage
```bash
python main.py
```

### Advanced Usage
```bash
python main.py hydra_configs/default.yaml
```

## Controls

- **Alt**: Start/stop recording
- **Q**: Quit application
- **Space**: Toggle recording (if configured)

## Troubleshooting

### Common Issues

1. **Missing Model Files**
   - Run `python setup.py` to check
   - Download from official repository

2. **CUDA Issues**
   - Ensure CUDA is properly installed
   - Check GPU availability with `torch.cuda.is_available()`

3. **Permission Errors**
   - Ensure write permissions in current directory
   - Check webcam access permissions

4. **Import Errors**
   - Reinstall dependencies: `pip install -r requirements.txt`
   - Check Python version compatibility

### Debug Mode
```bash
python main.py --debug
```

## Performance Tips

1. **Use GPU**: Set `gpu_idx: 0` in default.yaml
2. **Optimize FPS**: Adjust `fps` parameter in main.py
3. **Reduce Resolution**: Lower `res_factor` for faster processing

## Support

For issues or questions:
1. Check this README first
2. Run `python setup.py` for diagnostics
3. Open an issue on GitHub

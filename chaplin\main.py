import torch
import hydra
import cv2
import time
from pipelines.pipeline import InferencePipeline
import numpy as np
from datetime import datetime
from ollama import chat
from pydantic import BaseModel
import keyboard
from concurrent.futures import ThreadPoolExecutor
import os
import warnings
from flask import Flask, request, jsonify


# Pydantic model for the chat output
class ChaplinOutput(BaseModel):
    list_of_changes: str
    corrected_text: str


class Chaplin:
    def __init__(self):
        self.vsr_model = None
        self.recording = False
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.output_prefix = "webcam"
        self.res_factor = 3
        self.fps = 16
        self.frame_interval = 1 / self.fps
        self.frame_compression = 25

    def perform_inference(self, video_path):
        output = self.vsr_model(video_path)
        keyboard.write(output)

        cmd = ",".join(["shift+left"] * len(output))
        keyboard.press_and_release(cmd)

        response = chat(
            model='llama3.2',
            messages=[
                {
                    'role': 'system',
                    'content': (
                        "You are an assistant that helps make corrections to the output of a lipreading model. "
                        "The text will likely be imperfect. Replace incorrect words, add proper punctuation, and "
                        "return the corrected text in the format 'list_of_changes' and 'corrected_text'."
                    )
                },
                {
                    'role': 'user',
                    'content': f"Transcription:\n\n{output}"
                }
            ],
            format=ChaplinOutput.model_json_schema()
        )

        chat_output = ChaplinOutput.model_validate_json(response.message.content)

        if chat_output.corrected_text[-1] not in ['.', '?', '!']:
            chat_output.corrected_text += '.'

        keyboard.write(chat_output.corrected_text + " ")

        return {
            "output": chat_output.corrected_text,
            "video_path": video_path
        }

    def start_webcam(self):
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640 // self.res_factor)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480 // self.res_factor)
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        last_frame_time = time.time()
        futures = []
        output_path = ""
        out = None
        frame_count = 0

        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                for file in os.listdir():
                    if file.startswith(self.output_prefix) and file.endswith('.mp4'):
                        os.remove(file)
                break

            current_time = time.time()

            if current_time - last_frame_time >= self.frame_interval:
                ret, frame = cap.read()
                if ret:
                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), self.frame_compression]
                    _, buffer = cv2.imencode('.jpg', frame, encode_param)
                    compressed_frame = cv2.imdecode(buffer, cv2.IMREAD_GRAYSCALE)

                    if self.recording:
                        if out is None:
                            output_path = self.output_prefix + str(time.time_ns() // 1_000_000) + '.mp4'
                            out = cv2.VideoWriter(
                                output_path,
                                cv2.VideoWriter_fourcc(*'mp4v'),
                                self.fps,
                                (frame_width, frame_height),
                                False
                            )

                        out.write(compressed_frame)
                        last_frame_time = current_time
                        cv2.circle(compressed_frame, (frame_width - 20, 20), 10, (0, 0, 0), -1)
                        frame_count += 1

                    elif not self.recording and frame_count > 0:
                        if out is not None:
                            out.release()

                        if frame_count >= self.fps * 2:
                            futures.append(self.executor.submit(self.perform_inference, output_path))
                        else:
                            os.remove(output_path)

                        output_path = self.output_prefix + str(time.time_ns() // 1_000_000) + '.mp4'
                        out = cv2.VideoWriter(
                            output_path,
                            cv2.VideoWriter_fourcc(*'mp4v'),
                            self.fps,
                            (frame_width, frame_height),
                            False
                        )
                        frame_count = 0

                    cv2.imshow('Chaplin', cv2.flip(compressed_frame, 1))

            for fut in futures[:]:
                if fut.done():
                    result = fut.result()
                    os.remove(result["video_path"])
                    futures.remove(fut)
                else:
                    break

        cap.release()
        if out:
            out.release()
        cv2.destroyAllWindows()

    def on_action(self, event):
        if event.event_type == keyboard.KEY_DOWN and event.name == 'alt':
            self.recording = not self.recording


@hydra.main(version_base=None, config_path="hydra_configs", config_name="default")
def main(cfg):
    chaplin = Chaplin()
    keyboard.hook(lambda e: chaplin.on_action(e))

    chaplin.vsr_model = InferencePipeline(
        config_filename=cfg.config_filename,
        device=torch.device(f"cuda:{cfg.gpu_idx}" if torch.cuda.is_available() and cfg.gpu_idx >= 0 else "cpu"),
        detector=cfg.detector,
        face_track=True
    )
    print("✅ Model loaded successfully!")

    chaplin.start_webcam()


if __name__ == '__main__':
    main()
